<?php

namespace App\Controllers;

/**
 * Company Controller
 * 
 * Manages company settings, subscriptions, and multi-tenant administration
 * Super admin can manage all companies, company admins manage their own company
 */
class CompanyController extends BaseController
{
    /**
     * Display companies list (Super Admin only)
     */
    public function index()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);
        
        try {
            // Get filter parameters
            $status = $_GET['status'] ?? '';
            $plan = $_GET['plan'] ?? '';
            $search = $_GET['search'] ?? '';
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = 20;
            $offset = ($page - 1) * $limit;

            // Build query
            $where = ["1=1"];
            $params = [];

            if (!empty($status)) {
                $where[] = "c.status = ?";
                $params[] = $status;
            }

            if (!empty($plan)) {
                $where[] = "c.subscription_plan = ?";
                $params[] = $plan;
            }

            if (!empty($search)) {
                $where[] = "(c.name LIKE ? OR c.email LIKE ? OR c.domain LIKE ?)";
                $searchTerm = "%{$search}%";
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }

            $whereClause = implode(' AND ', $where);

            // Get companies with statistics
            $companies = $this->db->fetchAll(
                "SELECT c.*, 
                        COUNT(DISTINCT u.id) as user_count,
                        COUNT(DISTINCT d.id) as document_count,
                        COUNT(DISTINCT b.id) as bundle_count,
                        COUNT(DISTINCT box.id) as box_count,
                        SUM(d.file_size) as storage_used,
                        (c.storage_used / c.storage_limit * 100) as storage_percentage
                 FROM companies c
                 LEFT JOIN users u ON c.id = u.company_id AND u.status = 'active'
                 LEFT JOIN documents d ON c.id = d.company_id AND d.status != 'deleted'
                 LEFT JOIN bundles b ON c.id = b.company_id AND b.status = 'active'
                 LEFT JOIN boxes box ON c.id = box.company_id
                 WHERE {$whereClause}
                 GROUP BY c.id
                 ORDER BY c.created_at DESC
                 LIMIT ? OFFSET ?",
                array_merge($params, [$limit, $offset])
            );

            // Get total count
            $totalCount = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM companies c WHERE {$whereClause}",
                $params
            );

            // Get summary statistics
            $summaryStats = $this->getCompanySummaryStats();

            $this->view('companies/index', [
                'title' => 'Company Management',
                'companies' => $companies,
                'totalCount' => $totalCount,
                'currentPage' => $page,
                'totalPages' => ceil($totalCount / $limit),
                'summaryStats' => $summaryStats,
                'filters' => [
                    'status' => $status,
                    'plan' => $plan,
                    'search' => $search
                ]
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading companies: ' . $e->getMessage(), 'error');
            $this->redirect('/dashboard');
        }
    }

    /**
     * Show company details
     */
    public function show($id = null)
    {
        $this->requireAuth();
        
        // Super admin can view any company, others can only view their own
        if ($this->user['role'] === 'super_admin') {
            $companyId = $id ?? $this->user['company_id'];
        } else {
            $this->requireRole(['company_admin']);
            $companyId = $this->user['company_id'];
        }
        
        try {
            $company = $this->getCompanyById($companyId);
            if (!$company) {
                throw new \Exception('Company not found');
            }

            // Get company statistics
            $companyStats = $this->getCompanyStats($companyId);

            // Get company users
            $companyUsers = $this->getCompanyUsers($companyId);

            // Get recent activity
            $recentActivity = $this->getCompanyActivity($companyId);

            $this->view('companies/show', [
                'title' => $company['name'],
                'company' => $company,
                'companyStats' => $companyStats,
                'companyUsers' => $companyUsers,
                'recentActivity' => $recentActivity
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading company: ' . $e->getMessage(), 'error');
            $this->redirect($this->user['role'] === 'super_admin' ? '/app/companies' : '/dashboard');
        }
    }

    /**
     * Show create company form (Super Admin only)
     */
    public function create()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);
        
        $subscriptionPlans = config('subscription_plans', []);

        $this->view('companies/create', [
            'title' => 'Create New Company',
            'subscriptionPlans' => $subscriptionPlans
        ]);
    }

    /**
     * Store new company (Super Admin only)
     */
    public function store()
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);
        
        try {
            // Validate input
            $data = $this->validate($_POST, [
                'name' => 'required|max:255',
                'domain' => 'max:255',
                'email' => 'required|email|max:255',
                'phone' => 'max:20',
                'address' => 'max:1000',
                'subscription_plan' => 'required|in:basic,premium,enterprise',
                'storage_limit' => 'integer|min:1',
                'admin_first_name' => 'required|max:100',
                'admin_last_name' => 'required|max:100',
                'admin_email' => 'required|email|max:255',
                'admin_username' => 'required|max:50',
                'admin_password' => 'required|min:8'
            ]);

            // Check if domain is unique
            if (!empty($data['domain'])) {
                $existing = $this->db->fetch(
                    "SELECT id FROM companies WHERE domain = ?",
                    [$data['domain']]
                );
                if ($existing) {
                    throw new \Exception('Domain already exists');
                }
            }

            // Check if admin email is unique
            $existingUser = $this->db->fetch(
                "SELECT id FROM users WHERE email = ?",
                [$data['admin_email']]
            );
            if ($existingUser) {
                throw new \Exception('Admin email already exists');
            }

            // Convert storage limit from GB to bytes
            $storageLimitBytes = isset($data['storage_limit']) ?
                $data['storage_limit'] * 1073741824 : // GB to bytes
                5368709120; // Default 5GB in bytes

            // Create company
            $companyId = $this->db->execute(
                "INSERT INTO companies (
                    name, domain, email, phone, address, subscription_plan,
                    storage_limit, storage_used, status, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, 0, 'active', NOW(), NOW())",
                [
                    $data['name'],
                    $data['domain'] ?? null,
                    $data['email'],
                    $data['phone'] ?? null,
                    $data['address'] ?? null,
                    $data['subscription_plan'],
                    $storageLimitBytes
                ]
            );

            // Create company admin user
            $adminUserId = $this->db->execute(
                "INSERT INTO users (
                    company_id, first_name, last_name, email, username, password_hash,
                    role, status, email_verified, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, 'company_admin', 'active', 1, NOW(), NOW())",
                [
                    $companyId,
                    $data['admin_first_name'],
                    $data['admin_last_name'],
                    $data['admin_email'],
                    $data['admin_username'],
                    password_hash($data['admin_password'], PASSWORD_DEFAULT)
                ]
            );

            // Log activity
            $this->logActivity('create', 'company', $companyId, "Created company: {$data['name']}");

            $this->setFlashMessage('Company created successfully', 'success');
            $this->redirect("/app/companies/{$companyId}");

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to create company: ' . $e->getMessage(), 'error');
            $this->redirect('/app/companies/create');
        }
    }

    /**
     * Show edit company form
     */
    public function edit($id = null)
    {
        $this->requireAuth();
        
        // Super admin can edit any company, others can only edit their own
        if ($this->user['role'] === 'super_admin') {
            $companyId = $id ?? $this->user['company_id'];
        } else {
            $this->requireRole(['company_admin']);
            $companyId = $this->user['company_id'];
        }
        
        try {
            $company = $this->getCompanyById($companyId);
            if (!$company) {
                throw new \Exception('Company not found');
            }

            $subscriptionPlans = config('subscription_plans', []);

            $this->view('companies/edit', [
                'title' => 'Edit Company - ' . $company['name'],
                'company' => $company,
                'subscriptionPlans' => $subscriptionPlans
            ]);

        } catch (\Exception $e) {
            $this->setFlashMessage('Error loading company: ' . $e->getMessage(), 'error');
            $this->redirect($this->user['role'] === 'super_admin' ? '/app/companies' : '/dashboard');
        }
    }

    /**
     * Update company
     */
    public function update($id = null)
    {
        $this->requireAuth();
        
        // Super admin can update any company, others can only update their own
        if ($this->user['role'] === 'super_admin') {
            $companyId = $id ?? $this->user['company_id'];
        } else {
            $this->requireRole(['company_admin']);
            $companyId = $this->user['company_id'];
        }
        
        try {
            $company = $this->getCompanyById($companyId);
            if (!$company) {
                throw new \Exception('Company not found');
            }

            // Validate input
            $data = $this->validate($_POST, [
                'name' => 'required|max:255',
                'domain' => 'max:255',
                'email' => 'required|email|max:255',
                'phone' => 'max:20',
                'address' => 'max:1000',
                'subscription_plan' => 'in:basic,premium,enterprise',
                'storage_limit' => 'integer|min:1',
                'status' => 'in:active,suspended,inactive'
            ]);

            // Check if domain is unique (excluding current company)
            if (!empty($data['domain']) && $data['domain'] !== $company['domain']) {
                $existing = $this->db->fetch(
                    "SELECT id FROM companies WHERE domain = ? AND id != ?",
                    [$data['domain'], $companyId]
                );
                if ($existing) {
                    throw new \Exception('Domain already exists');
                }
            }

            // Build update query
            $updateFields = [];
            $updateParams = [];

            foreach (['name', 'domain', 'email', 'phone', 'address'] as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "{$field} = ?";
                    $updateParams[] = $data[$field];
                }
            }

            // Super admin can update subscription and status
            if ($this->user['role'] === 'super_admin') {
                if (isset($data['subscription_plan'])) {
                    $updateFields[] = "subscription_plan = ?";
                    $updateParams[] = $data['subscription_plan'];
                }
                if (isset($data['storage_limit'])) {
                    $updateFields[] = "storage_limit = ?";
                    $updateParams[] = $data['storage_limit'] * 1073741824; // Convert GB to bytes
                }
                if (isset($data['status'])) {
                    $updateFields[] = "status = ?";
                    $updateParams[] = $data['status'];
                }
            }

            if (!empty($updateFields)) {
                $updateFields[] = "updated_at = NOW()";
                $updateParams[] = $companyId;

                $this->db->execute(
                    "UPDATE companies SET " . implode(', ', $updateFields) . " WHERE id = ?",
                    $updateParams
                );
            }

            // Log activity
            $this->logActivity('update', 'company', $companyId, "Updated company: {$data['name']}");

            $this->setFlashMessage('Company updated successfully', 'success');
            $this->redirect("/app/companies/{$companyId}");

        } catch (\Exception $e) {
            $this->setFlashMessage('Failed to update company: ' . $e->getMessage(), 'error');
            $this->redirect("/app/companies/{$companyId}/edit");
        }
    }

    /**
     * Get company by ID
     */
    private function getCompanyById($id)
    {
        return $this->db->fetch(
            "SELECT * FROM companies WHERE id = ?",
            [$id]
        );
    }

    /**
     * Get company summary statistics
     */
    private function getCompanySummaryStats()
    {
        $stats = [];

        try {
            // Total companies
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM companies");
            $stats['total_companies'] = $result['count'] ?? 0;

            // Active companies
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM companies WHERE status = 'active'");
            $stats['active_companies'] = $result['count'] ?? 0;

            // Companies by plan
            $planStats = $this->db->fetchAll(
                "SELECT subscription_plan, COUNT(*) as count FROM companies GROUP BY subscription_plan"
            );
            $stats['by_plan'] = [];
            foreach ($planStats as $row) {
                $stats['by_plan'][$row['subscription_plan']] = $row['count'];
            }

            // Total storage used
            $result = $this->db->fetch("SELECT SUM(storage_used) as total FROM companies");
            $stats['total_storage_used'] = $result['total'] ?? 0;

        } catch (\Exception $e) {
            // Return default stats on error
        }

        return $stats;
    }

    /**
     * Get company statistics
     */
    private function getCompanyStats($companyId)
    {
        $stats = [];

        try {
            // User count
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM users WHERE company_id = ? AND status = 'active'",
                [$companyId]
            );
            $stats['user_count'] = $result['count'] ?? 0;

            // Document count
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM documents WHERE company_id = ? AND status != 'deleted'",
                [$companyId]
            );
            $stats['document_count'] = $result['count'] ?? 0;

            // Bundle count
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM bundles WHERE company_id = ? AND status = 'active'",
                [$companyId]
            );
            $stats['bundle_count'] = $result['count'] ?? 0;

            // Box count
            $result = $this->db->fetch(
                "SELECT COUNT(*) as count FROM boxes WHERE company_id = ?",
                [$companyId]
            );
            $stats['box_count'] = $result['count'] ?? 0;

        } catch (\Exception $e) {
            // Return default stats on error
        }

        return $stats;
    }

    /**
     * Get company users
     */
    private function getCompanyUsers($companyId)
    {
        try {
            return $this->db->fetchAll(
                "SELECT * FROM users WHERE company_id = ? ORDER BY created_at DESC",
                [$companyId]
            );
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get company activity
     */
    private function getCompanyActivity($companyId)
    {
        try {
            return $this->db->fetchAll(
                "SELECT al.*, u.first_name, u.last_name
                 FROM activity_logs al
                 LEFT JOIN users u ON al.user_id = u.id
                 WHERE al.company_id = ?
                 ORDER BY al.created_at DESC
                 LIMIT 20",
                [$companyId]
            );
        } catch (\Exception $e) {
            return [];
        }
    }



    /**
     * Update company status (AJAX endpoint)
     */
    public function updateStatus($id)
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        if (!isAjax()) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid request']);
            return;
        }

        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $status = $input['status'] ?? '';

            if (!in_array($status, ['active', 'suspended', 'blocked', 'inactive'])) {
                throw new \Exception('Invalid status');
            }

            $company = $this->getCompanyById($id);
            if (!$company) {
                throw new \Exception('Company not found');
            }

            // Update company status
            $this->db->execute(
                "UPDATE companies SET status = ?, updated_at = NOW() WHERE id = ?",
                [$status, $id]
            );

            // Log activity
            $this->logActivity('update', 'company', $id, "Changed company status to: {$status}");

            echo json_encode([
                'success' => true,
                'message' => 'Company status updated successfully'
            ]);

        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'error' => true,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Delete company (AJAX endpoint)
     */
    public function delete($id)
    {
        $this->requireAuth();
        $this->requireRole(['super_admin']);

        if (!isAjax()) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid request']);
            return;
        }

        try {
            $company = $this->getCompanyById($id);
            if (!$company) {
                throw new \Exception('Company not found');
            }

            // Start transaction
            $this->db->beginTransaction();

            try {
                // Get list of existing tables to avoid errors
                $existingTables = $this->getExistingTables();

                // Get all users associated with this company
                $users = [];
                if (in_array('users', $existingTables)) {
                    $users = $this->db->fetchAll(
                        "SELECT id, email FROM users WHERE company_id = ?",
                        [$id]
                    );
                }

                // Step 1: Delete records that reference users (foreign key constraints)
                foreach ($users as $user) {
                    $userId = $user['id'];

                    // Delete user sessions
                    if (in_array('user_sessions', $existingTables)) {
                        $this->db->execute("DELETE FROM user_sessions WHERE user_id = ?", [$userId]);
                    }

                    // Delete user notifications
                    if (in_array('notifications', $existingTables)) {
                        $this->db->execute("DELETE FROM notifications WHERE user_id = ?", [$userId]);
                    }

                    // Delete records where user is referenced as foreign key
                    if (in_array('box_bundles', $existingTables)) {
                        $this->db->execute("DELETE FROM box_bundles WHERE added_by = ?", [$userId]);
                    }

                    if (in_array('document_bundles', $existingTables)) {
                        $this->db->execute("DELETE FROM document_bundles WHERE added_by = ?", [$userId]);
                    }

                    if (in_array('bundle_requests', $existingTables)) {
                        $this->db->execute("DELETE FROM bundle_requests WHERE requested_by = ?", [$userId]);
                        $this->db->execute("DELETE FROM bundle_requests WHERE approved_by = ?", [$userId]);
                    }

                    if (in_array('document_versions', $existingTables)) {
                        $this->db->execute("DELETE FROM document_versions WHERE uploaded_by = ?", [$userId]);
                    }

                    if (in_array('document_shares', $existingTables)) {
                        $this->db->execute("DELETE FROM document_shares WHERE shared_by = ?", [$userId]);
                        $this->db->execute("DELETE FROM document_shares WHERE shared_with = ?", [$userId]);
                    }

                    if (in_array('audit_logs', $existingTables)) {
                        $this->db->execute("DELETE FROM audit_logs WHERE user_id = ?", [$userId]);
                    }

                    // Delete user activity logs
                    if (in_array('activity_logs', $existingTables)) {
                        $this->db->execute("DELETE FROM activity_logs WHERE user_id = ?", [$userId]);
                    }
                }

                // Step 2: Delete company-related data in correct order (respecting foreign keys)

                // Delete document-related data first
                if (in_array('document_requests', $existingTables)) {
                    $this->db->execute("DELETE FROM document_requests WHERE company_id = ?", [$id]);
                }

                if (in_array('documents', $existingTables)) {
                    $this->db->execute("DELETE FROM documents WHERE company_id = ?", [$id]);
                }

                // Delete bundle-related data
                if (in_array('bundles', $existingTables)) {
                    $this->db->execute("DELETE FROM bundles WHERE company_id = ?", [$id]);
                }

                // Delete box-related data
                if (in_array('boxes', $existingTables)) {
                    $this->db->execute("DELETE FROM boxes WHERE company_id = ?", [$id]);
                }

                // Delete other company data
                if (in_array('automated_alerts', $existingTables)) {
                    $this->db->execute("DELETE FROM automated_alerts WHERE company_id = ?", [$id]);
                }

                if (in_array('company_settings', $existingTables)) {
                    $this->db->execute("DELETE FROM company_settings WHERE company_id = ?", [$id]);
                }

                // Step 3: Delete users (now that all references are gone)
                if (in_array('users', $existingTables)) {
                    $this->db->execute("DELETE FROM users WHERE company_id = ?", [$id]);
                }

                // Step 4: Delete remaining activity logs for this company
                if (in_array('activity_logs', $existingTables)) {
                    $this->db->execute("DELETE FROM activity_logs WHERE company_id = ?", [$id]);
                }

                // Step 5: Finally, delete the company
                $this->db->execute("DELETE FROM companies WHERE id = ?", [$id]);

                // Commit transaction
                $this->db->commit();

                // Log the deletion activity (system-wide log) - only if we can
                try {
                    $this->logActivity('delete', 'company', $id, "Permanently deleted company: {$company['name']} and all associated data");
                } catch (\Exception $logError) {
                    // If logging fails, don't fail the whole operation
                    error_log("Failed to log company deletion: " . $logError->getMessage());
                }

                echo json_encode([
                    'success' => true,
                    'message' => 'Company and all associated data have been permanently deleted'
                ]);

            } catch (\Exception $e) {
                // Rollback transaction on error
                $this->db->rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'error' => true,
                'message' => 'Failed to delete company: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get list of existing database tables
     */
    private function getExistingTables()
    {
        try {
            $tables = $this->db->fetchAll("SHOW TABLES");
            return array_column($tables, array_keys($tables[0])[0]);
        } catch (\Exception $e) {
            // If we can't get table list, assume basic tables exist
            return ['companies', 'users', 'activity_logs'];
        }
    }
}
