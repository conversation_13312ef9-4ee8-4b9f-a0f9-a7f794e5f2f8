<?php
$title = $title ?? 'Super Admin Dashboard';
$page_css = [url('/assets/css/super-admin-dashboard.css')];
$page_js = [url('/assets/js/super-admin-dashboard.js')];
ob_start();
?>

<div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50" id="dashboard-container">
    
    <!-- Enhanced Header -->
    <div class="bg-white/95 backdrop-blur-md border-b border-white/30 shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div class="mb-4 lg:mb-0">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-blue-900 bg-clip-text text-transparent">
                                Super Admin Dashboard
                            </h1>
                            <p class="text-gray-600 mt-1">System-wide overview and management</p>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                    <div class="flex items-center space-x-4">
                        <div class="text-right">
                            <p class="text-sm text-gray-500">System Status</p>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                <p class="font-medium text-green-600">Online</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-500">Last Updated</p>
                            <p class="font-medium text-gray-700" id="last-updated">
                                <?= date('H:i:s') ?>
                            </p>
                        </div>
                    </div>
                    <button onclick="refreshDashboard()"
                            class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 shadow-md hover:shadow-lg">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Enhanced Quick Actions -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <a href="<?= url('/app/companies') ?>"
                   class="group flex flex-col items-center p-4 bg-white/90 backdrop-blur-sm border border-white/30 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
                    <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-medium text-gray-700 mt-2 text-center">Companies</span>
                </a>

                <a href="<?= url('/app/users') ?>"
                   class="group flex flex-col items-center p-4 bg-white/90 backdrop-blur-sm border border-white/30 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
                    <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center group-hover:bg-green-200 transition-colors">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-medium text-gray-700 mt-2 text-center">Users</span>
                </a>

                <a href="<?= url('/super-admin/analytics') ?>"
                   class="group flex flex-col items-center p-4 bg-white/90 backdrop-blur-sm border border-white/30 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
                    <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center group-hover:bg-purple-200 transition-colors">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-medium text-gray-700 mt-2 text-center">Analytics</span>
                </a>

                <a href="<?= url('/super-admin/settings') ?>"
                   class="group flex flex-col items-center p-4 bg-white/90 backdrop-blur-sm border border-white/30 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
                    <div class="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center group-hover:bg-gray-200 transition-colors">
                        <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-medium text-gray-700 mt-2 text-center">Settings</span>
                </a>

                <a href="<?= url('/app/documents') ?>"
                   class="group flex flex-col items-center p-4 bg-white/90 backdrop-blur-sm border border-white/30 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
                    <div class="w-12 h-12 bg-indigo-100 rounded-xl flex items-center justify-center group-hover:bg-indigo-200 transition-colors">
                        <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-medium text-gray-700 mt-2 text-center">Documents</span>
                </a>

                <a href="<?= url('/app/reports') ?>"
                   class="group flex flex-col items-center p-4 bg-white/90 backdrop-blur-sm border border-white/30 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
                    <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center group-hover:bg-orange-200 transition-colors">
                        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-medium text-gray-700 mt-2 text-center">Reports</span>
                </a>
            </div>
        </div>

        <!-- System Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">

            <!-- Total Companies -->
            <div class="stat-card glass-effect rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 icon-container icon-blue rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Companies</p>
                        <p class="text-2xl font-bold text-gray-900" data-stat="total_companies"><?= number_format($systemStats['total_companies']) ?></p>
                        <p class="text-xs text-green-600" data-stat="active_companies"><?= number_format($systemStats['active_companies']) ?> active</p>
                    </div>
                </div>
            </div>

            <!-- Total Users -->
            <div class="stat-card glass-effect rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 icon-container icon-green rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Users</p>
                        <p class="text-2xl font-bold text-gray-900" data-stat="total_users"><?= number_format($systemStats['total_users']) ?></p>
                        <p class="text-xs text-green-600" data-stat="active_users"><?= number_format($systemStats['active_users']) ?> active</p>
                    </div>
                </div>
            </div>

            <!-- Total Documents -->
            <div class="stat-card glass-effect rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 icon-container icon-purple rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Documents</p>
                        <p class="text-2xl font-bold text-gray-900" data-stat="total_documents"><?= number_format($systemStats['total_documents']) ?></p>
                        <p class="text-xs text-blue-600" data-stat="total_bundles"><?= number_format($systemStats['total_bundles']) ?> bundles</p>
                    </div>
                </div>
            </div>

            <!-- Storage Usage -->
            <div class="stat-card glass-effect rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 icon-container icon-orange rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Storage Usage</p>
                        <p class="text-2xl font-bold text-gray-900" data-stat="storage_percentage"><?= $systemStats['storage_percentage'] ?>%</p>
                        <p class="text-xs text-gray-600"><?= formatFileSize($systemStats['total_storage_used']) ?> / <?= formatFileSize($systemStats['total_storage_limit']) ?></p>
                    </div>
                </div>
            </div>

        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            <!-- Company Overview -->
            <div class="lg:col-span-2 company-table glass-effect rounded-2xl p-6 shadow-lg">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Company Overview</h3>
                    <a href="<?= url('/app/companies') ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        View all →
                    </a>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b border-gray-200">
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Company</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Plan</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Users</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Storage</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($companyOverview, 0, 8) as $company): ?>
                                <tr class="border-b border-gray-100 hover:bg-gray-50">
                                    <td class="py-3 px-4">
                                        <div>
                                            <p class="font-medium text-gray-900"><?= e($company['name']) ?></p>
                                            <p class="text-sm text-gray-500"><?= e($company['email']) ?></p>
                                        </div>
                                    </td>
                                    <td class="py-3 px-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-<?= $company['subscription_plan'] === 'enterprise' ? 'purple' : ($company['subscription_plan'] === 'premium' ? 'blue' : 'green') ?>-100 text-<?= $company['subscription_plan'] === 'enterprise' ? 'purple' : ($company['subscription_plan'] === 'premium' ? 'blue' : 'green') ?>-800">
                                            <?= ucfirst($company['subscription_plan']) ?>
                                        </span>
                                    </td>
                                    <td class="py-3 px-4 text-sm text-gray-900"><?= number_format($company['user_count']) ?></td>
                                    <td class="py-3 px-4">
                                        <div class="flex items-center">
                                            <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                <div class="bg-<?= $company['storage_percentage'] > 80 ? 'red' : ($company['storage_percentage'] > 60 ? 'yellow' : 'green') ?>-500 h-2 rounded-full" style="width: <?= min(100, $company['storage_percentage']) ?>%"></div>
                                            </div>
                                            <span class="text-xs text-gray-600"><?= round($company['storage_percentage'], 1) ?>%</span>
                                        </div>
                                    </td>
                                    <td class="py-3 px-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-<?= $company['status'] === 'active' ? 'green' : 'red' ?>-100 text-<?= $company['status'] === 'active' ? 'green' : 'red' ?>-800">
                                            <?= ucfirst($company['status']) ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- System Alerts & Recent Activity -->
            <div class="space-y-6">
                
                <!-- System Alerts -->
                <div class="alert-card glass-effect rounded-2xl p-6 shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">System Alerts</h3>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <?= count($systemAlerts) ?> active
                        </span>
                    </div>
                    
                    <div class="space-y-3">
                        <?php if (!empty($systemAlerts)): ?>
                            <?php foreach (array_slice($systemAlerts, 0, 5) as $alert): ?>
                                <div class="flex items-start p-3 bg-<?= $alert['severity'] === 'critical' ? 'red' : 'yellow' ?>-50 rounded-lg">
                                    <div class="flex-shrink-0">
                                        <svg class="w-5 h-5 text-<?= $alert['severity'] === 'critical' ? 'red' : 'yellow' ?>-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <h4 class="text-sm font-medium text-gray-900"><?= e($alert['title']) ?></h4>
                                        <p class="text-sm text-gray-600 mt-1"><?= e($alert['message']) ?></p>
                                        <p class="text-xs text-gray-500 mt-1"><?= e($alert['company_name']) ?></p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-gray-500 text-sm">No active alerts</p>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mt-4">
                        <a href="<?= url('/app/alerts') ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            View all alerts →
                        </a>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="stat-card glass-effect rounded-2xl p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Total Boxes</span>
                            <span class="font-medium text-gray-900"><?= number_format($systemStats['total_boxes']) ?></span>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Pending Requests</span>
                            <span class="font-medium text-gray-900"><?= number_format($systemStats['pending_requests']) ?></span>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Active Alerts</span>
                            <span class="font-medium text-gray-900"><?= number_format($systemStats['active_alerts']) ?></span>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">System Uptime</span>
                            <span class="font-medium text-green-600">99.9%</span>
                        </div>
                    </div>
                </div>

            </div>

        </div>

    </div>
</div>

<style>
/* Ensure proper background colors for dashboard cards */
.stat-card,
.company-table,
.alert-card,
.glass-effect {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* Override any conflicting Tailwind classes */
.bg-white\/90 {
    background: rgba(255, 255, 255, 0.95) !important;
}

/* Ensure the page background gradient is visible */
#dashboard-container {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #e0f2fe 100%) !important;
}
</style>

<script>
// Auto-refresh dashboard every 5 minutes
setTimeout(() => {
    location.reload();
}, 300000);

// Format file size helper function
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Ensure cards have proper background on load
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.stat-card, .company-table, .alert-card, .glass-effect');
    cards.forEach(card => {
        card.style.background = 'rgba(255, 255, 255, 0.95)';
        card.style.backdropFilter = 'blur(20px)';
        card.style.border = '1px solid rgba(255, 255, 255, 0.3)';
    });
});
</script>

<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
