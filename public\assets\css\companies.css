/**
 * Companies Management Styles
 * Enhanced styling for the companies management interface
 */

/* Enhanced table styles */
.companies-table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.companies-table th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    font-weight: 600;
    letter-spacing: 0.025em;
}

.companies-table tr:hover {
    background-color: #f8fafc;
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

/* Status badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-active {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.status-suspended {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
}

.status-inactive {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

/* Plan badges */
.plan-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.plan-basic {
    background-color: #dcfce7;
    color: #166534;
}

.plan-premium {
    background-color: #dbeafe;
    color: #1e40af;
}

.plan-enterprise {
    background-color: #f3e8ff;
    color: #7c3aed;
}

/* Action buttons */
.action-button {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    text-decoration: none;
}

.action-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.action-view {
    background-color: #dbeafe;
    color: #1e40af;
}

.action-view:hover {
    background-color: #bfdbfe;
    color: #1d4ed8;
}

.action-edit {
    background-color: #e0e7ff;
    color: #4338ca;
}

.action-edit:hover {
    background-color: #c7d2fe;
    color: #3730a3;
}

/* Dropdown menus */
.dropdown-menu {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    color: #374151;
    text-align: left;
    border: none;
    background: none;
    transition: all 0.15s ease;
}

.dropdown-item:hover {
    background-color: #f9fafb;
    color: #111827;
}

.dropdown-divider {
    height: 1px;
    background-color: #e5e7eb;
    margin: 0.25rem 0;
}

/* Storage progress bars */
.storage-progress {
    width: 100%;
    height: 0.5rem;
    background-color: #e5e7eb;
    border-radius: 9999px;
    overflow: hidden;
}

.storage-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 9999px;
    transition: width 0.3s ease;
}

.storage-progress-bar.warning {
    background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
}

.storage-progress-bar.danger {
    background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
}

/* Search and filter section */
.filter-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-input {
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.filter-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

/* Summary stats cards */
.stats-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.stats-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stats-icon.blue {
    background-color: #dbeafe;
    color: #1e40af;
}

.stats-icon.green {
    background-color: #dcfce7;
    color: #166534;
}

.stats-icon.purple {
    background-color: #f3e8ff;
    color: #7c3aed;
}

.stats-icon.orange {
    background-color: #fed7aa;
    color: #ea580c;
}

/* Responsive enhancements */
@media (max-width: 768px) {
    .companies-table {
        font-size: 0.875rem;
    }
    
    .action-button {
        padding: 0.375rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .stats-card {
        padding: 1rem;
    }
}

/* Loading states */
.loading-overlay {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
}

.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Notification styles */
.notification {
    border-radius: 0.75rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Enhanced form styles for create/edit pages */
.form-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
}

.form-input {
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.form-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

.form-button {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.form-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}
