<?php
/**
 * Minimal test to isolate the 500 error
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Minimal Test</h1>";

try {
    // Test 1: Basic constants
    echo "<h2>Test 1: Constants</h2>";
    define('APP_ROOT', dirname(__DIR__));
    define('PUBLIC_ROOT', __DIR__);
    define('CONFIG_PATH', APP_ROOT . '/src/config');
    echo "Constants defined successfully<br>";

    // Test 2: Autoloader
    echo "<h2>Test 2: Autoloader</h2>";
    require_once APP_ROOT . '/src/autoload.php';
    echo "Autoloader loaded successfully<br>";

    // Test 3: Configuration
    echo "<h2>Test 3: Configuration</h2>";
    require_once CONFIG_PATH . '/constants.php';
    echo "Constants loaded successfully<br>";
    
    $config = require_once CONFIG_PATH . '/app.php';
    echo "App config loaded successfully<br>";

    // Test 4: Router instantiation
    echo "<h2>Test 4: Router</h2>";
    $router = new \App\Core\Router();
    echo "Router created successfully<br>";

    // Test 5: Simple route handling
    echo "<h2>Test 5: Route Test</h2>";
    $router->get('/test', function() {
        echo "Test route works!<br>";
    });
    
    // Test the route
    $router->handleRequest('GET', '/test');
    
} catch (Exception $e) {
    echo "<h2>ERROR CAUGHT:</h2>";
    echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<h3>Stack Trace:</h3>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
} catch (Error $e) {
    echo "<h2>FATAL ERROR CAUGHT:</h2>";
    echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<h3>Stack Trace:</h3>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<h2>Test Complete</h2>";
?>
