<?php
/**
 * Debug script to identify the cause of HTTP 500 error
 */

// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>DMS Debug - HTTP 500 Error Investigation</h1>";

// Test 1: Basic PHP functionality
echo "<h2>Test 1: Basic PHP</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Current time: " . date('Y-m-d H:i:s') . "<br>";

// Test 2: File system access
echo "<h2>Test 2: File System</h2>";
$appRoot = dirname(__DIR__);
echo "App Root: " . $appRoot . "<br>";
echo "App Root exists: " . (is_dir($appRoot) ? 'YES' : 'NO') . "<br>";
echo "App Root readable: " . (is_readable($appRoot) ? 'YES' : 'NO') . "<br>";

// Test 3: Autoloader
echo "<h2>Test 3: Autoloader</h2>";
$autoloadPath = $appRoot . '/src/autoload.php';
echo "Autoload path: " . $autoloadPath . "<br>";
echo "Autoload exists: " . (file_exists($autoloadPath) ? 'YES' : 'NO') . "<br>";

try {
    require_once $autoloadPath;
    echo "Autoloader loaded: YES<br>";
} catch (Exception $e) {
    echo "Autoloader error: " . $e->getMessage() . "<br>";
}

// Test 4: Configuration
echo "<h2>Test 4: Configuration</h2>";
$configPath = $appRoot . '/src/config';
echo "Config path: " . $configPath . "<br>";
echo "Config exists: " . (is_dir($configPath) ? 'YES' : 'NO') . "<br>";

$constantsPath = $configPath . '/constants.php';
echo "Constants file: " . $constantsPath . "<br>";
echo "Constants exists: " . (file_exists($constantsPath) ? 'YES' : 'NO') . "<br>";

try {
    if (file_exists($constantsPath)) {
        require_once $constantsPath;
        echo "Constants loaded: YES<br>";
    }
} catch (Exception $e) {
    echo "Constants error: " . $e->getMessage() . "<br>";
}

$appConfigPath = $configPath . '/app.php';
echo "App config file: " . $appConfigPath . "<br>";
echo "App config exists: " . (file_exists($appConfigPath) ? 'YES' : 'NO') . "<br>";

try {
    if (file_exists($appConfigPath)) {
        $config = require_once $appConfigPath;
        echo "App config loaded: YES<br>";
        echo "Debug mode: " . ($config['debug'] ?? 'NOT SET') . "<br>";
    }
} catch (Exception $e) {
    echo "App config error: " . $e->getMessage() . "<br>";
}

// Test 5: Router class
echo "<h2>Test 5: Router Class</h2>";
try {
    if (class_exists('\\App\\Core\\Router')) {
        echo "Router class exists: YES<br>";
        $router = new \App\Core\Router();
        echo "Router instantiated: YES<br>";
    } else {
        echo "Router class exists: NO<br>";
    }
} catch (Exception $e) {
    echo "Router error: " . $e->getMessage() . "<br>";
}

// Test 6: Routes file
echo "<h2>Test 6: Routes</h2>";
$routesPath = $appRoot . '/src/routes.php';
echo "Routes file: " . $routesPath . "<br>";
echo "Routes exists: " . (file_exists($routesPath) ? 'YES' : 'NO') . "<br>";

try {
    if (file_exists($routesPath)) {
        require_once $routesPath;
        echo "Routes loaded: YES<br>";
    }
} catch (Exception $e) {
    echo "Routes error: " . $e->getMessage() . "<br>";
}

// Test 7: Database connection
echo "<h2>Test 7: Database</h2>";
try {
    if (class_exists('\\App\\Core\\Database')) {
        $db = \App\Core\Database::getInstance();
        echo "Database connection: YES<br>";
    } else {
        echo "Database class not found<br>";
    }
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "<br>";
}

echo "<h2>Debug Complete</h2>";
echo "If you see this message, the basic PHP setup is working.<br>";
echo "Check the tests above for any errors that might cause the 500 error in the main application.";
?>
