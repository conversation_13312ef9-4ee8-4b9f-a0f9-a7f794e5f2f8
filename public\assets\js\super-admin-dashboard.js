/**
 * Super Admin Dashboard JavaScript
 * Enhanced functionality and real-time updates
 */

class SuperAdminDashboard {
    constructor() {
        this.refreshInterval = null;
        this.lastUpdateTime = new Date();
        this.isRefreshing = false;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.startAutoRefresh();
        this.animateOnLoad();
        this.setupRealTimeUpdates();
    }
    
    setupEventListeners() {
        // Refresh button
        const refreshBtn = document.querySelector('[onclick="refreshDashboard()"]');
        if (refreshBtn) {
            refreshBtn.removeAttribute('onclick');
            refreshBtn.addEventListener('click', () => this.refreshDashboard());
        }
        
        // Quick action cards hover effects
        this.setupQuickActionEffects();
        
        // Statistics cards animations
        this.setupStatCardAnimations();
        
        // Company table interactions
        this.setupTableInteractions();
    }
    
    setupQuickActionEffects() {
        const quickActions = document.querySelectorAll('.quick-action-card, [class*="group flex flex-col"]');
        
        quickActions.forEach(card => {
            card.addEventListener('mouseenter', (e) => {
                this.animateCard(e.target, 'hover');
            });
            
            card.addEventListener('mouseleave', (e) => {
                this.animateCard(e.target, 'leave');
            });
        });
    }
    
    setupStatCardAnimations() {
        const statCards = document.querySelectorAll('.stat-card, [class*="bg-white/90 backdrop-blur-sm"]');
        
        statCards.forEach((card, index) => {
            // Stagger animation on load
            setTimeout(() => {
                card.classList.add('fade-in');
            }, index * 100);
            
            // Add hover effects
            card.addEventListener('mouseenter', () => {
                this.animateStatCard(card, true);
            });
            
            card.addEventListener('mouseleave', () => {
                this.animateStatCard(card, false);
            });
        });
    }
    
    setupTableInteractions() {
        const tableRows = document.querySelectorAll('.company-table tbody tr, table tbody tr');
        
        tableRows.forEach(row => {
            row.addEventListener('click', (e) => {
                if (!e.target.closest('a')) {
                    this.highlightRow(row);
                }
            });
        });
    }
    
    animateCard(card, type) {
        const icon = card.querySelector('.w-12.h-12');
        
        if (type === 'hover') {
            if (icon) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
            }
            card.style.transform = 'translateY(-4px)';
        } else {
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
            card.style.transform = 'translateY(0)';
        }
    }
    
    animateStatCard(card, isHover) {
        const icon = card.querySelector('.w-12.h-12, .w-6.h-6');
        
        if (isHover) {
            card.style.transform = 'translateY(-8px) scale(1.02)';
            if (icon) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
            }
        } else {
            card.style.transform = 'translateY(0) scale(1)';
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
        }
    }
    
    highlightRow(row) {
        // Remove previous highlights
        document.querySelectorAll('.row-highlighted').forEach(r => {
            r.classList.remove('row-highlighted');
        });
        
        // Add highlight to clicked row
        row.classList.add('row-highlighted');
        
        // Remove highlight after 2 seconds
        setTimeout(() => {
            row.classList.remove('row-highlighted');
        }, 2000);
    }
    
    animateOnLoad() {
        // Animate elements on page load
        const elements = document.querySelectorAll('.stat-card, .quick-action-card, .company-table, .alert-card');
        
        elements.forEach((element, index) => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                element.style.transition = 'all 0.5s ease-out';
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }
    
    startAutoRefresh() {
        // Update time every second
        setInterval(() => {
            this.updateLastUpdatedTime();
        }, 1000);
        
        // Auto-refresh dashboard every 5 minutes
        this.refreshInterval = setInterval(() => {
            this.refreshDashboard(true);
        }, 300000); // 5 minutes
    }
    
    updateLastUpdatedTime() {
        const timeElement = document.getElementById('last-updated');
        if (timeElement) {
            const now = new Date();
            const timeDiff = Math.floor((now - this.lastUpdateTime) / 1000);
            
            if (timeDiff < 60) {
                timeElement.textContent = `${timeDiff}s ago`;
            } else if (timeDiff < 3600) {
                timeElement.textContent = `${Math.floor(timeDiff / 60)}m ago`;
            } else {
                timeElement.textContent = now.toLocaleTimeString();
            }
        }
    }
    
    async refreshDashboard(isAuto = false) {
        if (this.isRefreshing) return;
        
        this.isRefreshing = true;
        
        try {
            // Show loading state
            this.showLoadingState();
            
            // Fetch updated data
            const response = await fetch('/dms/public/super-admin/dashboard-data', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                this.updateDashboardData(data);
                this.lastUpdateTime = new Date();
                
                if (!isAuto) {
                    this.showNotification('Dashboard refreshed successfully', 'success');
                }
            } else {
                throw new Error('Failed to refresh dashboard');
            }
        } catch (error) {
            console.error('Dashboard refresh error:', error);
            if (!isAuto) {
                this.showNotification('Failed to refresh dashboard', 'error');
            }
        } finally {
            this.hideLoadingState();
            this.isRefreshing = false;
        }
    }
    
    updateDashboardData(data) {
        // Update statistics
        if (data.systemStats) {
            this.updateStatistics(data.systemStats);
        }
        
        // Update company overview
        if (data.companyOverview) {
            this.updateCompanyOverview(data.companyOverview);
        }
        
        // Update alerts
        if (data.systemAlerts) {
            this.updateSystemAlerts(data.systemAlerts);
        }
    }
    
    updateStatistics(stats) {
        const statElements = {
            'total_companies': document.querySelector('[data-stat="total_companies"]'),
            'active_companies': document.querySelector('[data-stat="active_companies"]'),
            'total_users': document.querySelector('[data-stat="total_users"]'),
            'active_users': document.querySelector('[data-stat="active_users"]'),
            'total_documents': document.querySelector('[data-stat="total_documents"]'),
            'storage_percentage': document.querySelector('[data-stat="storage_percentage"]')
        };
        
        Object.keys(statElements).forEach(key => {
            const element = statElements[key];
            if (element && stats[key] !== undefined) {
                this.animateNumberChange(element, stats[key]);
            }
        });
    }
    
    animateNumberChange(element, newValue) {
        const currentValue = parseInt(element.textContent.replace(/[^\d]/g, '')) || 0;
        const difference = newValue - currentValue;
        
        if (difference === 0) return;
        
        const duration = 1000; // 1 second
        const steps = 30;
        const stepValue = difference / steps;
        const stepDuration = duration / steps;
        
        let currentStep = 0;
        
        const interval = setInterval(() => {
            currentStep++;
            const value = Math.round(currentValue + (stepValue * currentStep));
            
            if (key === 'storage_percentage') {
                element.textContent = value + '%';
            } else {
                element.textContent = this.formatNumber(value);
            }
            
            if (currentStep >= steps) {
                clearInterval(interval);
                element.textContent = this.formatNumber(newValue);
            }
        }, stepDuration);
    }
    
    formatNumber(num) {
        return new Intl.NumberFormat().format(num);
    }
    
    showLoadingState() {
        const refreshBtn = document.querySelector('[onclick*="refreshDashboard"], button[onclick*="refreshDashboard"]');
        if (refreshBtn) {
            const icon = refreshBtn.querySelector('svg');
            if (icon) {
                icon.classList.add('animate-spin');
            }
            refreshBtn.disabled = true;
        }
    }
    
    hideLoadingState() {
        const refreshBtn = document.querySelector('[onclick*="refreshDashboard"], button[onclick*="refreshDashboard"]');
        if (refreshBtn) {
            const icon = refreshBtn.querySelector('svg');
            if (icon) {
                icon.classList.remove('animate-spin');
            }
            refreshBtn.disabled = false;
        }
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 max-w-sm w-full bg-white rounded-lg shadow-lg border-l-4 p-4 ${
            type === 'success' ? 'border-green-400' : 
            type === 'error' ? 'border-red-400' : 
            'border-blue-400'
        } transform translate-x-full transition-transform duration-300`;
        
        notification.innerHTML = `
            <div class="flex">
                <div class="flex-shrink-0">
                    ${type === 'success' ? 
                        '<svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' :
                      type === 'error' ? 
                        '<svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>' :
                        '<svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
                    }
                </div>
                <div class="ml-3">
                    <p class="text-sm text-gray-700">${message}</p>
                </div>
                <div class="ml-auto pl-3">
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-gray-400 hover:text-gray-600">
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Slide in
        setTimeout(() => {
            notification.style.transform = 'translate-x-0';
        }, 100);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.style.transform = 'translate-x-full';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 5000);
    }
    
    setupRealTimeUpdates() {
        // Setup WebSocket or Server-Sent Events for real-time updates
        // This is a placeholder for future real-time functionality
        console.log('Real-time updates initialized');
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.superAdminDashboard = new SuperAdminDashboard();
});

// Global function for backward compatibility
function refreshDashboard() {
    if (window.superAdminDashboard) {
        window.superAdminDashboard.refreshDashboard();
    }
}
