<?php
$title = 'Login';
$page_css = [url('/assets/css/login.css')];
$content = ob_get_clean();
ob_start();
?>

<div class="min-h-screen flex items-center justify-center relative overflow-hidden">
    <!-- Animated Background -->
    <div class="absolute inset-0 bg-gradient-to-br from-indigo-50 via-white to-cyan-50"></div>

    <!-- Floating Shapes -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
        <div class="absolute top-40 left-40 w-80 h-80 bg-gradient-to-br from-blue-400 to-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
    </div>

    <!-- Geometric Patterns -->
    <div class="absolute inset-0 opacity-5">
        <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" stroke-width="0.5"/>
                </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
        </svg>
    </div>

    <div class="relative z-10 max-w-md w-full mx-4 space-y-8">
        <!-- Logo and Header -->
        <div class="text-center animate-fade-in-up">
            <!-- Logo Container with Glow Effect -->
            <div class="relative mx-auto w-20 h-20 mb-6">
                <div class="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 rounded-2xl blur-lg opacity-75 animate-pulse-slow"></div>
                <div class="relative w-20 h-20 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-2xl transform hover:scale-105 transition-transform duration-300">
                    <svg class="h-10 w-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
            </div>

            <!-- Title with Gradient Text -->
            <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent mb-2">
                DMS Login
            </h1>
            <p class="text-gray-600 text-lg font-medium mb-1">
                Document Management System
            </p>
            <div class="flex items-center justify-center space-x-2 text-sm text-gray-500">
                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span>Secure Access</span>
            </div>

            <!-- Decorative Elements -->
            <div class="flex justify-center mt-4 space-x-1">
                <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                <div class="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-2 h-2 bg-indigo-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
        </div>
        
        <!-- Error Messages -->
        <?php if (isset($error)): ?>
            <div class="bg-red-50 border-l-4 border-red-400 rounded-lg p-4 shadow-sm">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-red-800"><?= e($error) ?></p>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <?php if (isset($errors) && !empty($errors) && is_array($errors)): ?>
            <div class="bg-red-50 border-l-4 border-red-400 rounded-lg p-4 shadow-sm">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Validation Errors</h3>
                        <div class="mt-2 text-sm text-red-700">
                            <ul class="list-disc pl-5 space-y-1">
                                <?php foreach ($errors as $field => $fieldErrors): ?>
                                    <?php if (is_array($fieldErrors)): ?>
                                        <?php foreach ($fieldErrors as $error): ?>
                                            <li><?= e($error) ?></li>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <li><?= e($fieldErrors) ?></li>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Login Form Card -->
        <div class="relative animate-fade-in-up animation-delay-300">
            <!-- Card Glow Effect -->
            <div class="absolute -inset-1 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 rounded-2xl blur opacity-25"></div>

            <!-- Main Card -->
            <div class="relative bg-white/80 backdrop-blur-xl py-8 px-8 shadow-2xl rounded-2xl border border-white/20">
                <form class="space-y-6" action="<?= url('/login') ?>" method="POST" id="login-form">
                    <!-- CSRF Token -->
                    <?php if (isset($_SESSION['csrf_token'])): ?>
                        <input type="hidden" name="_token" value="<?= $_SESSION['csrf_token'] ?>">
                    <?php endif; ?>

                    <div class="space-y-5">
                        <!-- Email Field -->
                        <div class="group">
                            <label for="email" class="block text-sm font-semibold text-gray-700 mb-2 group-focus-within:text-blue-600 transition-colors duration-200">
                                Email Address
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                    </svg>
                                </div>
                                <input id="email" name="email" type="email" autocomplete="email" required
                                       class="block w-full pl-12 pr-4 py-4 bg-gray-50/50 border border-gray-200 rounded-xl placeholder-gray-400 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white transition-all duration-200 hover:bg-gray-50 sm:text-sm"
                                       placeholder="<EMAIL>"
                                       value="<?= e($email ?? '') ?>">
                                <!-- Focus Ring -->
                                <div class="absolute inset-0 rounded-xl ring-2 ring-blue-500 ring-opacity-0 group-focus-within:ring-opacity-20 transition-all duration-200 pointer-events-none"></div>
                            </div>
                        </div>

                        <!-- Password Field -->
                        <div class="group">
                            <label for="password" class="block text-sm font-semibold text-gray-700 mb-2 group-focus-within:text-blue-600 transition-colors duration-200">
                                Password
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                    </svg>
                                </div>
                                <input id="password" name="password" type="password" autocomplete="current-password" required
                                       class="block w-full pl-12 pr-12 py-4 bg-gray-50/50 border border-gray-200 rounded-xl placeholder-gray-400 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white transition-all duration-200 hover:bg-gray-50 sm:text-sm"
                                       placeholder="Enter your password">
                                <!-- Show/Hide Password Button -->
                                <button type="button" class="absolute inset-y-0 right-0 pr-4 flex items-center" onclick="togglePassword()">
                                    <svg id="eye-icon" class="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </button>
                                <!-- Focus Ring -->
                                <div class="absolute inset-0 rounded-xl ring-2 ring-blue-500 ring-opacity-0 group-focus-within:ring-opacity-20 transition-all duration-200 pointer-events-none"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Options Row -->
                    <div class="flex items-center justify-between pt-2">
                        <div class="flex items-center group">
                            <div class="relative">
                                <input id="remember" name="remember" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-all duration-200 hover:border-blue-400">
                                <div class="absolute inset-0 rounded border-2 border-blue-500 opacity-0 group-hover:opacity-20 transition-opacity duration-200"></div>
                            </div>
                            <label for="remember" class="ml-3 block text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors duration-200 cursor-pointer">
                                Keep me signed in
                            </label>
                        </div>

                        <div class="text-sm">
                            <a href="<?= url('/forgot-password') ?>" class="font-semibold text-blue-600 hover:text-blue-700 transition-colors duration-200 hover:underline">
                                Forgot password?
                            </a>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="pt-4">
                        <button type="submit" class="group relative w-full overflow-hidden" id="login-button">
                            <!-- Button Background with Gradient -->
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 transition-all duration-300 group-hover:scale-105"></div>
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-700 via-purple-700 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                            <!-- Button Content -->
                            <div class="relative flex items-center justify-center py-4 px-6 rounded-xl text-white font-semibold text-base transition-all duration-300 group-hover:transform group-hover:scale-105">
                                <!-- Icon -->
                                <svg class="h-5 w-5 mr-3 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                                </svg>

                                <!-- Text -->
                                <span id="login-text" class="transition-all duration-300">Sign In</span>

                                <!-- Loading Spinner -->
                                <span id="login-spinner" class="hidden">
                                    <svg class="animate-spin h-5 w-5 text-white ml-3" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </span>
                            </div>

                            <!-- Ripple Effect -->
                            <div class="absolute inset-0 rounded-xl overflow-hidden">
                                <div class="ripple absolute bg-white opacity-20 rounded-full transform scale-0 transition-transform duration-600"></div>
                            </div>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Security Notice -->
        <div class="text-center animate-fade-in-up animation-delay-600">
            <div class="inline-flex items-center justify-center space-x-3 px-6 py-3 bg-white/60 backdrop-blur-sm rounded-full border border-white/30 shadow-lg">
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <svg class="h-4 w-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <span class="text-sm font-medium text-gray-700">Enterprise Security</span>
            </div>

            <!-- Demo Credentials Helper -->
            <div class="mt-4 p-4 bg-blue-50/80 backdrop-blur-sm rounded-xl border border-blue-200/50">
                <p class="text-xs font-semibold text-blue-800 mb-2">Demo Credentials</p>
                <div class="text-xs text-blue-700 space-y-1">
                    <div class="flex justify-between items-center">
                        <span>Email:</span>
                        <code class="bg-blue-100 px-2 py-1 rounded text-blue-800"><EMAIL></code>
                    </div>
                    <div class="flex justify-between items-center">
                        <span>Password:</span>
                        <code class="bg-blue-100 px-2 py-1 rounded text-blue-800">admin123</code>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced JavaScript -->
<script>
// Password toggle functionality
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const eyeIcon = document.getElementById('eye-icon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        eyeIcon.innerHTML = `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
        `;
    } else {
        passwordInput.type = 'password';
        eyeIcon.innerHTML = `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
        `;
    }
}

// Enhanced form submission with animations
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('login-form');
    const button = document.getElementById('login-button');
    const buttonText = document.getElementById('login-text');
    const spinner = document.getElementById('login-spinner');

    // Add ripple effect to button
    button.addEventListener('click', function(e) {
        const ripple = button.querySelector('.ripple');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('scale-100');

        setTimeout(() => {
            ripple.classList.remove('scale-100');
        }, 600);
    });

    form.addEventListener('submit', function(e) {
        // Show loading state
        button.disabled = true;
        buttonText.classList.add('hidden');
        spinner.classList.remove('hidden');

        // Add loading animation to button
        button.classList.add('animate-pulse');

        // Re-enable button after 10 seconds as fallback
        setTimeout(() => {
            button.disabled = false;
            buttonText.classList.remove('hidden');
            spinner.classList.add('hidden');
            button.classList.remove('animate-pulse');
        }, 10000);
    });

    // Add floating animation to input fields
    const inputs = document.querySelectorAll('input[type="email"], input[type="password"]');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('transform', 'scale-105');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('transform', 'scale-105');
        });
    });
});
</script>



<?php
$content = ob_get_contents();
ob_end_clean();

// Include the layout
include APP_ROOT . '/src/views/layouts/app.php';
?>
