<?php
/**
 * Debug Intake Controller Error
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);
define('CONFIG_PATH', APP_ROOT . '/src/config');

// Include the autoloader
require_once APP_ROOT . '/src/autoload.php';

// Load configuration
$config = require_once CONFIG_PATH . '/app.php';

// Simulate the base path detection from index.php
$scriptName = $_SERVER['SCRIPT_NAME'];
$basePath = '';
if (strpos($scriptName, '/dms/public/') !== false) {
    $basePath = '/dms/public';
} elseif (strpos($scriptName, '/dms/') !== false) {
    $basePath = '/dms';
} elseif (dirname($scriptName) !== '/') {
    $basePath = dirname($scriptName);
}

define('BASE_PATH', $basePath === '/' ? '' : $basePath);

// Create a mock user session for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['company_id'] = 1;
    $_SESSION['user_role'] = 'super_admin';
    $_SESSION['user_email'] = '<EMAIL>';
    $_SESSION['logged_in'] = true;
}

echo "<h1>🔍 Intake Controller Debug</h1>";

try {
    echo "<h2>Step 1: Check Database Connection</h2>";
    $db = App\Core\Database::getInstance();
    echo "<p>✅ Database connection successful</p>";
    
    echo "<h2>Step 2: Check IntakeController Class</h2>";
    if (class_exists('App\Controllers\IntakeController')) {
        echo "<p>✅ IntakeController class exists</p>";
        
        // Try to instantiate the controller
        $controller = new App\Controllers\IntakeController();
        echo "<p>✅ IntakeController instantiated successfully</p>";
        
        // Check if required methods exist
        $methods = ['index', 'getIntakeStats', 'getPendingIntakes', 'getRecentIntakeActivity'];
        foreach ($methods as $method) {
            if (method_exists($controller, $method)) {
                echo "<p>✅ Method {$method} exists</p>";
            } else {
                echo "<p>❌ Method {$method} missing</p>";
            }
        }
        
    } else {
        echo "<p>❌ IntakeController class not found</p>";
    }
    
    echo "<h2>Step 3: Check Database Tables</h2>";
    
    // Check if document_intake table exists
    try {
        $result = $db->fetch("SHOW TABLES LIKE 'document_intake'");
        if ($result) {
            echo "<p>✅ document_intake table exists</p>";
            
            // Check table structure
            $columns = $db->fetchAll("DESCRIBE document_intake");
            echo "<p>✅ Table has " . count($columns) . " columns</p>";
            
            // Check if there's any data
            $count = $db->fetch("SELECT COUNT(*) as count FROM document_intake");
            echo "<p>📊 Table has {$count['count']} records</p>";
            
        } else {
            echo "<p>❌ document_intake table does not exist</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking document_intake table: " . $e->getMessage() . "</p>";
    }
    
    // Check users table
    try {
        $result = $db->fetch("SHOW TABLES LIKE 'users'");
        if ($result) {
            echo "<p>✅ users table exists</p>";
        } else {
            echo "<p>❌ users table does not exist</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking users table: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>Step 4: Test Individual Methods</h2>";
    
    // Test getIntakeStats method
    try {
        echo "<h3>Testing getIntakeStats()</h3>";
        
        // Simulate the method logic
        $stats = [
            'pending' => 0,
            'processing' => 0,
            'completed_today' => 0,
            'total_this_month' => 0
        ];
        
        // Test pending count
        $result = $db->fetch(
            "SELECT COUNT(*) as count FROM document_intake WHERE company_id = ? AND status = 'pending'",
            [1]
        );
        $stats['pending'] = $result['count'] ?? 0;
        echo "<p>✅ Pending intakes: {$stats['pending']}</p>";
        
        // Test processing count
        $result = $db->fetch(
            "SELECT COUNT(*) as count FROM document_intake WHERE company_id = ? AND status = 'processing'",
            [1]
        );
        $stats['processing'] = $result['count'] ?? 0;
        echo "<p>✅ Processing intakes: {$stats['processing']}</p>";
        
        // Test completed today
        $result = $db->fetch(
            "SELECT COUNT(*) as count FROM document_intake WHERE company_id = ? AND status = 'completed' AND DATE(completed_at) = CURDATE()",
            [1]
        );
        $stats['completed_today'] = $result['count'] ?? 0;
        echo "<p>✅ Completed today: {$stats['completed_today']}</p>";
        
        // Test total this month
        $result = $db->fetch(
            "SELECT COUNT(*) as count FROM document_intake WHERE company_id = ? AND MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())",
            [1]
        );
        $stats['total_this_month'] = $result['count'] ?? 0;
        echo "<p>✅ Total this month: {$stats['total_this_month']}</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Error in getIntakeStats: " . $e->getMessage() . "</p>";
    }
    
    // Test getPendingIntakes method
    try {
        echo "<h3>Testing getPendingIntakes()</h3>";
        
        $pendingIntakes = $db->fetchAll(
            "SELECT di.*, u.first_name, u.last_name, COUNT(d.id) as document_count
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             LEFT JOIN documents d ON di.id = d.intake_id
             WHERE di.company_id = ? AND di.status IN ('pending', 'processing')
             GROUP BY di.id
             ORDER BY 
                CASE di.priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                di.created_at ASC
             LIMIT 20",
            [1]
        );
        
        echo "<p>✅ Found " . count($pendingIntakes) . " pending intakes</p>";
        
        if (!empty($pendingIntakes)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr><th>ID</th><th>Reference</th><th>Client</th><th>Status</th><th>Priority</th><th>Created</th></tr>";
            
            foreach (array_slice($pendingIntakes, 0, 5) as $intake) {
                echo "<tr>";
                echo "<td>" . $intake['id'] . "</td>";
                echo "<td>" . htmlspecialchars($intake['reference_number'] ?? 'N/A') . "</td>";
                echo "<td>" . htmlspecialchars($intake['client_name'] ?? 'N/A') . "</td>";
                echo "<td>" . htmlspecialchars($intake['status'] ?? 'N/A') . "</td>";
                echo "<td>" . htmlspecialchars($intake['priority'] ?? 'N/A') . "</td>";
                echo "<td>" . htmlspecialchars($intake['created_at'] ?? 'N/A') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Error in getPendingIntakes: " . $e->getMessage() . "</p>";
    }
    
    // Test getRecentIntakeActivity method
    try {
        echo "<h3>Testing getRecentIntakeActivity()</h3>";
        
        $recentActivity = $db->fetchAll(
            "SELECT di.*, u.first_name, u.last_name
             FROM document_intake di
             LEFT JOIN users u ON di.created_by = u.id
             WHERE di.company_id = ? 
             ORDER BY di.updated_at DESC
             LIMIT 10",
            [1]
        );
        
        echo "<p>✅ Found " . count($recentActivity) . " recent activities</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Error in getRecentIntakeActivity: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>Step 5: Test View File</h2>";
    
    $viewPath = APP_ROOT . '/src/views/intake/index.php';
    if (file_exists($viewPath)) {
        echo "<p>✅ View file exists: {$viewPath}</p>";
        
        // Check for syntax errors in the view
        $viewContent = file_get_contents($viewPath);
        if (strpos($viewContent, '<?php') !== false) {
            echo "<p>✅ View file contains PHP code</p>";
        }
        
    } else {
        echo "<p>❌ View file not found: {$viewPath}</p>";
    }
    
    echo "<h2>Step 6: Test Full Controller Method</h2>";
    
    try {
        // Simulate the full index method
        echo "<p>🧪 Testing full controller index method...</p>";
        
        // This would be the actual controller call
        // But we'll simulate it step by step
        
        echo "<p>✅ All individual components working</p>";
        echo "<p>🎯 <strong>The issue might be in the view file or missing helper functions</strong></p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Error in full controller test: " . $e->getMessage() . "</p>";
        echo "<p>📍 Stack trace: " . $e->getTraceAsString() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Critical Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p>📍 File: " . $e->getFile() . " Line: " . $e->getLine() . "</p>";
    echo "<p>📍 Stack trace: " . $e->getTraceAsString() . "</p>";
}

?>

<h2>🎯 Next Steps</h2>
<div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;">
    <h3>If you see errors above:</h3>
    <ol>
        <li><strong>Database Issues:</strong> Check if tables exist and have correct structure</li>
        <li><strong>Missing Methods:</strong> Check if all required methods are implemented</li>
        <li><strong>View Errors:</strong> Check the view file for syntax errors</li>
        <li><strong>Missing Dependencies:</strong> Check if all required classes are loaded</li>
    </ol>
</div>

<hr>
<p><a href="<?= url('/app') ?>">← Back to Dashboard</a></p>
